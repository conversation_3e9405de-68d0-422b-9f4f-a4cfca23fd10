package cn.iocoder.yudao.module.bpm.service.task.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.FlowableUtils;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.el.FixedValue;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通用表单变更审批监听器
 * 审批通过后，关联到台账流程实例并同步基础数据（使用Flowable历史变量作为台账数据）
 * 
 * 使用方式：
 * 1. 在BPMN流程设计器中配置ExecutionListener
 * 2. 监听器类型：delegateExpression
 * 3. 监听器表达式：${bpmGenericFormChangeListener}
 * 4. 监听事件：end（流程结束时触发）
 * 5. 扩展字段配置：
 *    字段名：listenerConfig
 *    字段值：{"relationField":"关联字段名"}
 * 
 * 示例配置：
 * 扩展字段值：{"relationField":"contract_id"}
 * 
 * 工作原理：
 * 1. 监听表单变更审批流程结束事件
 * 2. 检查流程是否审批通过
 * 3. 获取变更流程的表单数据
 * 4. 根据关联字段值查找对应的台账流程实例
 * 5. 更新台账流程实例的历史变量：
 *    - applicant: 更新为当前流程发起人
 *    - change_time: 更新为当前时间
 *    - 其他字段: 直接覆盖（除了特殊字段）
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BpmGenericFormChangeListener implements ExecutionListener {

    @Resource
    private HistoryService historyService;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Setter
    private FixedValue listenerConfig;

    // 添加构造函数来确认Bean是否被正确创建
    public BpmGenericFormChangeListener() {
        log.info("BpmGenericFormChangeListener Bean 已创建");
    }

    /**
     * 监听器配置参数
     */
    @Setter
    @Getter
    public static class ListenerConfig {
        private String relationField;
        private String changeRecordField; // 变更记录字段名，如 "basicInfoChangeRecord"
        private Map<String, String> fieldMapping; // 字段映射关系，如 {"a": "b"}
        private java.util.List<String> skipFields; // 跳过的字段列表
        private Boolean recordOnly; // 仅记录模式，只添加变更记录，不更新其他字段

        @Override
        public String toString() {
            return "ListenerConfig{" +
                    "relationField='" + relationField + '\'' +
                    ", changeRecordField='" + changeRecordField + '\'' +
                    ", fieldMapping=" + fieldMapping +
                    ", skipFields=" + skipFields +
                    ", recordOnly=" + recordOnly +
                    '}';
        }
    }

    @Override
    public void notify(DelegateExecution execution) {
        log.info("=== BpmGenericFormChangeListener.notify() 被调用 ===");
        log.info("流程实例ID: {}", execution.getProcessInstanceId());
        log.info("当前活动ID: {}", execution.getCurrentActivityId());
        log.info("事件名称: {}", execution.getEventName());

        try {
            // 0. 快速检查流程状态 - 在配置解析前就检查，避免不必要的处理
            log.info("快速检查流程状态...");
            Map<String, Object> quickVariables = execution.getVariables();
            Object quickStatus = quickVariables.get("status");
            Object quickProcessStatus = quickVariables.get("PROCESS_STATUS");

            log.info("快速状态检查: status={}, PROCESS_STATUS={}", quickStatus, quickProcessStatus);

            // 如果明确是拒绝或取消状态，立即返回
            if (quickStatus != null && (BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(quickStatus) ||
                                       BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(quickStatus))) {
                log.warn("流程被拒绝或取消，status: {}, 跳过所有处理", quickStatus);
                return;
            }

            if (quickProcessStatus != null && (BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(quickProcessStatus) ||
                                              BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(quickProcessStatus))) {
                log.warn("流程被拒绝或取消，PROCESS_STATUS: {}, 跳过所有处理", quickProcessStatus);
                return;
            }

            // 1. 解析监听器配置
            log.info("开始解析监听器配置...");
            ListenerConfig config = parseConfig();
            log.info("解析监听器配置结果: {}", config);

            if (config == null) {
                log.error("监听器配置为空，请检查BPMN中的扩展字段配置！");
                log.error("正确的配置示例：");
                log.error("扩展字段名: listenerConfig");
                log.error("扩展字段值: {{\"relationField\":\"customerAbbreviation\",\"changeRecordField\":\"basicInfoChangeRecord\"}}");
                return;
            }
            if (StrUtil.isBlank(config.getRelationField())) {
                log.error("关联字段为空，请检查配置！当前配置: {}", config);
                log.error("relationField是必填字段，用于关联台账流程实例");
                return;
            }
            log.info("监听器配置解析成功，关联字段: {}, 变更记录字段: {}",
                    config.getRelationField(), config.getChangeRecordField());

            // 2. 获取流程实例的历史数据
            log.info("开始获取流程实例历史数据...");
            HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(execution.getProcessInstanceId())
                    .includeProcessVariables()
                    .singleResult();

            if (processInstance == null) {
                log.warn("未找到流程实例: {}", execution.getProcessInstanceId());
                return;
            }
            log.info("成功获取流程实例: {}, 开始时间: {}, 结束时间: {}",
                    processInstance.getId(), processInstance.getStartTime(), processInstance.getEndTime());

            // 3. 检查审批状态 - 只处理审核通过的end事件
            log.info("检查流程审批状态...");

            Map<String, Object> executionVariables = execution.getVariables();
            log.info("执行变量: {}", executionVariables);

            Map<String, Object> processVariables = processInstance.getProcessVariables();
            log.info("流程变量: {}", processVariables);

            // 检查流程状态，只处理审核通过的情况
            Object statusVar = processVariables.get("status");
            Object processStatusVar = processVariables.get("PROCESS_STATUS");

            log.info("状态检查 - status: {}, PROCESS_STATUS: {}", statusVar, processStatusVar);

            // 第一步：立即检查是否被拒绝或取消，如果是则直接返回
            if (statusVar != null && BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(statusVar)) {
                log.warn("流程实例 {} 被拒绝，status: {}, 跳过处理", execution.getProcessInstanceId(), statusVar);
                return;
            }
            if (statusVar != null && BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(statusVar)) {
                log.warn("流程实例 {} 被取消，status: {}, 跳过处理", execution.getProcessInstanceId(), statusVar);
                return;
            }
            if (processStatusVar != null && BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(processStatusVar)) {
                log.warn("流程实例 {} 被拒绝，PROCESS_STATUS: {}, 跳过处理", execution.getProcessInstanceId(), processStatusVar);
                return;
            }
            if (processStatusVar != null && BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(processStatusVar)) {
                log.warn("流程实例 {} 被取消，PROCESS_STATUS: {}, 跳过处理", execution.getProcessInstanceId(), processStatusVar);
                return;
            }

            // 第二步：检查是否为审核通过状态
            boolean isApproved = false;

            // 方法1：检查PROCESS_STATUS变量（优先级最高，因为这是流程引擎设置的最终状态）
            if (processStatusVar != null && BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(processStatusVar)) {
                isApproved = true;
                log.info("通过PROCESS_STATUS变量确认为审核通过，PROCESS_STATUS={}", processStatusVar);
            }

            // 方法2：检查status变量
            if (!isApproved && statusVar != null && BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(statusVar)) {
                isApproved = true;
                log.info("通过status变量确认为审核通过，status={}", statusVar);
            }

            // 方法3：对于end事件，如果没有明确的拒绝/取消状态，视为审核通过
            if (!isApproved && "end".equals(execution.getEventName())) {
                // 检查是否有明确的拒绝或取消状态
                boolean hasRejectOrCancelStatus = false;

                if (statusVar != null && (BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(statusVar) ||
                                         BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(statusVar))) {
                    hasRejectOrCancelStatus = true;
                }
                if (processStatusVar != null && (BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(processStatusVar) ||
                                                BpmProcessInstanceStatusEnum.CANCEL.getStatus().equals(processStatusVar))) {
                    hasRejectOrCancelStatus = true;
                }

                // 如果没有拒绝或取消状态，且是end事件，视为审核通过
                if (!hasRejectOrCancelStatus) {
                    isApproved = true;
                    log.info("end事件触发且无拒绝/取消状态，视为审核通过");
                }
            }

            if (!isApproved) {
                log.warn("流程实例 {} 未确认为审核通过状态，跳过处理。status={}, PROCESS_STATUS={}",
                        execution.getProcessInstanceId(), statusVar, processStatusVar);
                return;
            }

            log.info("确认流程审核通过，继续处理...");

            // 4. 获取表单数据
            log.info("获取表单数据...");
            Map<String, Object> formVariables = FlowableUtils.getProcessInstanceFormVariable(processInstance);
            log.info("表单数据: {}", formVariables);
            if (CollUtil.isEmpty(formVariables)) {
                log.warn("流程实例 {} 没有表单数据", execution.getProcessInstanceId());
                return;
            }

            // 5. 更新台账流程实例
            log.info("开始更新台账流程实例...");
            updateLedgerProcessInstance(config, formVariables, processInstance);

            log.info("表单变更审批监听器处理完成，流程实例: {}, 关联字段: {}",
                    execution.getProcessInstanceId(), config.getRelationField());

        } catch (Exception e) {
            log.error("表单变更审批监听器处理失败，流程实例: {}", execution.getProcessInstanceId(), e);
        }
    }

    /**
     * 解析监听器配置
     */
    private ListenerConfig parseConfig() {
        log.info("开始解析监听器配置...");
        if (listenerConfig == null) {
            log.warn("listenerConfig为null，请检查BPMN中是否正确配置了扩展字段");
            return null;
        }

        String expressionText = null;
        try {
            expressionText = listenerConfig.getExpressionText();
        } catch (Exception e) {
            log.error("获取配置文本失败: {}", e.getMessage());
            return null;
        }

        log.info("获取到的配置文本: {}", expressionText);
        if (StrUtil.isBlank(expressionText)) {
            log.warn("配置文本为空，请检查BPMN中扩展字段的配置");
            return null;
        }

        try {
            ListenerConfig config = JsonUtils.parseObject(expressionText, ListenerConfig.class);
            log.info("JSON解析成功: {}", config);
            return config;
        } catch (Exception e) {
            log.error("JSON解析失败，配置文本: {}", expressionText, e);
            return null;
        }
    }

    /**
     * 更新台账流程实例
     */
    private void updateLedgerProcessInstance(ListenerConfig config, Map<String, Object> formVariables, HistoricProcessInstance changeProcessInstance) {
        try {
            log.info("开始更新台账流程实例，配置: {}", config);

            // 获取关联字段值
            Object relationValue = formVariables.get(config.getRelationField());
            log.info("关联字段 {} 的值: {}", config.getRelationField(), relationValue);

            if (relationValue == null) {
                log.warn("关联字段 {} 的值为空，无法更新台账数据", config.getRelationField());
                return;
            }

            // 直接通过流程实例ID查找台账流程实例
            log.info("通过流程实例ID查找台账流程实例，ID: {}", relationValue);

            HistoricProcessInstance ledgerProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(relationValue.toString())
                    .includeProcessVariables()
                    .singleResult();

            if (ledgerProcessInstance == null) {
                log.error("未找到台账流程实例！");
                log.error("关联字段: {}, 流程实例ID: {}", config.getRelationField(), relationValue);
                log.error("请检查流程实例ID是否正确，以及台账流程实例是否存在");
                return;
            }

            log.info("成功找到台账流程实例: {}, 名称: {}, 状态: {}",
                    ledgerProcessInstance.getId(),
                    ledgerProcessInstance.getName(),
                    ledgerProcessInstance.getEndTime() != null ? "已完成" : "进行中");

            // 更新台账流程实例的历史变量
            updateLedgerVariables(ledgerProcessInstance.getId(), formVariables, changeProcessInstance, config);

            log.info("成功更新台账流程实例，台账流程ID: {}, 台账流程名称: {}, 关联字段: {}, 关联值: {}",
                    ledgerProcessInstance.getId(), ledgerProcessInstance.getName(), config.getRelationField(), relationValue);

        } catch (Exception e) {
            log.error("更新台账流程实例失败，关联字段: {}", config.getRelationField(), e);
            throw new RuntimeException("更新台账流程实例失败", e);
        }
    }

    /**
     * 更新台账流程实例的历史变量
     */
    private void updateLedgerVariables(String ledgerProcessInstanceId, Map<String, Object> formVariables,
                                     HistoricProcessInstance changeProcessInstance, ListenerConfig config) {
        try {
            log.info("开始更新台账历史变量，台账流程: {}", ledgerProcessInstanceId);

            // 检查是否为仅记录模式
            boolean isRecordOnly = config.getRecordOnly() != null && config.getRecordOnly();
            log.info("处理模式: {}", isRecordOnly ? "仅记录模式" : "完整更新模式");

            if (!isRecordOnly) {
                // 1. 更新表单字段（根据配置跳过指定字段）
                log.info("更新表单字段...");
                for (Map.Entry<String, Object> entry : formVariables.entrySet()) {
                    String fieldName = entry.getKey();

                    // 检查是否在跳过字段列表中
                    if (shouldSkipField(fieldName, config)) {
                        log.debug("跳过配置的字段: {}", fieldName);
                        continue;
                    }

                    // 检查台账中是否已存在该变量
                    if (!isVariableExistsInLedger(ledgerProcessInstanceId, fieldName)) {
                        log.debug("台账中不存在字段 {}，跳过更新", fieldName);
                        continue;
                    }

                    log.info("更新字段: {} = {}", fieldName, entry.getValue());
                    updateHistoricVariable(ledgerProcessInstanceId, fieldName, entry.getValue());
                }

                // 2. 更新特殊字段：申请人、变更时间
                log.info("更新特殊字段...");
                updateHistoricVariable(ledgerProcessInstanceId, "applicant", changeProcessInstance.getStartUserId());
                updateHistoricVariable(ledgerProcessInstanceId, "change_time", LocalDateTime.now());
                updateHistoricVariable(ledgerProcessInstanceId, "update_time", LocalDateTime.now());
            } else {
                log.info("仅记录模式：跳过字段更新，只处理变更记录");
            }

            // 3. 处理变更记录字段（如果配置了的话）
            if (StrUtil.isNotBlank(config.getChangeRecordField())) {
                log.info("处理变更记录字段: {}", config.getChangeRecordField());
                addChangeRecord(ledgerProcessInstanceId, formVariables, changeProcessInstance, config);
            }

        } catch (Exception e) {
            log.error("更新台账历史变量失败，台账流程: {}", ledgerProcessInstanceId, e);
            throw e;
        }
    }

    /**
     * 更新历史变量
     */
    private void updateHistoricVariable(String processInstanceId, String variableName, Object value) {
        try {
            // 处理值的序列化，避免双重JSON序列化
            String jsonValue;
            if (value instanceof String) {
                // 如果已经是字符串，直接使用
                jsonValue = (String) value;
            } else if (value instanceof Number || value instanceof Boolean) {
                // 数字和布尔值转为字符串
                jsonValue = String.valueOf(value);
            } else {
                // 复杂对象才进行JSON序列化
                jsonValue = JsonUtils.toJsonString(value);
            }
            
            // 检查是否已存在该变量
            List<Map<String, Object>> existingVars = jdbcTemplate.queryForList(
                    "SELECT ID_ FROM ACT_HI_VARINST WHERE PROC_INST_ID_ = ? AND NAME_ = ?", 
                    processInstanceId, variableName);

            if (CollUtil.isNotEmpty(existingVars)) {
                // 更新现有的历史变量
                String sql = "UPDATE ACT_HI_VARINST SET TEXT_ = ?, LAST_UPDATED_TIME_ = NOW() WHERE PROC_INST_ID_ = ? AND NAME_ = ?";
                int updated = jdbcTemplate.update(sql, jsonValue, processInstanceId, variableName);
                log.debug("更新历史变量: processInstanceId={}, variableName={}, updated={}", 
                        processInstanceId, variableName, updated);
            } else {
                // 插入新的历史变量记录
                String sql = "INSERT INTO ACT_HI_VARINST (ID_, PROC_INST_ID_, EXECUTION_ID_, TASK_ID_, NAME_, VAR_TYPE_, REV_, BYTEARRAY_ID_, DOUBLE_, LONG_, TEXT_, TEXT2_, CREATE_TIME_, LAST_UPDATED_TIME_) " +
                           "VALUES (?, ?, ?, NULL, ?, 'string', 1, NULL, NULL, NULL, ?, NULL, NOW(), NOW())";

                // 生成标准的UUID格式ID（与Flowable引擎保持一致）
                String varId = java.util.UUID.randomUUID().toString();

                log.debug("生成历史变量ID: {}, 长度: {}", varId, varId.length());

                int inserted = jdbcTemplate.update(sql, varId, processInstanceId, processInstanceId, variableName, jsonValue);
                log.debug("插入历史变量: processInstanceId={}, variableName={}, varId={}, inserted={}",
                        processInstanceId, variableName, varId, inserted);
            }
        } catch (Exception e) {
            log.error("更新历史变量失败: processInstanceId={}, variableName={}, value={}",
                    processInstanceId, variableName, value, e);
            // 不抛出异常，避免影响其他字段的更新
            log.warn("跳过字段 {} 的更新，继续处理其他字段", variableName);
        }
    }

    /**
     * 添加变更记录到台账流程实例
     */
    private void addChangeRecord(String ledgerProcessInstanceId, Map<String, Object> formVariables,
                               HistoricProcessInstance changeProcessInstance, ListenerConfig config) {
        try {
            log.info("开始添加变更记录，变更记录字段: {}", config.getChangeRecordField());

            // 1. 获取台账流程实例中现有的变更记录
            List<Map<String, Object>> existingChangeRecords = getExistingChangeRecords(ledgerProcessInstanceId, config.getChangeRecordField());
            log.info("台账中现有的变更记录数量: {}", existingChangeRecords.size());

            // 2. 根据字段映射构建新的变更记录
            Map<String, Object> newChangeRecord = buildChangeRecord(formVariables, changeProcessInstance, config);
            log.info("构建的新变更记录: {}", newChangeRecord);

            if (newChangeRecord.isEmpty()) {
                log.warn("没有构建出有效的变更记录，跳过添加");
                return;
            }

            // 3. 追加新的变更记录到现有记录中
            existingChangeRecords.add(newChangeRecord);
            log.info("追加后的总变更记录数量: {}", existingChangeRecords.size());

            // 4. 更新台账流程实例的变更记录字段（作为JSON数组）
            updateHistoricVariableAsJson(ledgerProcessInstanceId, config.getChangeRecordField(), existingChangeRecords);

            log.info("成功追加变更记录到台账流程实例: {}, 新增 1 条记录", ledgerProcessInstanceId);

        } catch (Exception e) {
            log.error("添加变更记录失败，台账流程: {}, 变更记录字段: {}",
                    ledgerProcessInstanceId, config.getChangeRecordField(), e);
            throw e;
        }
    }

    /**
     * 获取台账流程实例中现有的变更记录
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getExistingChangeRecords(String ledgerProcessInstanceId, String changeRecordField) {
        try {
            log.info("查询台账流程实例中的现有变更记录，流程ID: {}, 字段: {}", ledgerProcessInstanceId, changeRecordField);

            // 查询台账流程实例中的变更记录字段
            List<Map<String, Object>> existingVars = jdbcTemplate.queryForList(
                    "SELECT TEXT_ FROM ACT_HI_VARINST WHERE PROC_INST_ID_ = ? AND NAME_ = ?",
                    ledgerProcessInstanceId, changeRecordField);

            if (CollUtil.isEmpty(existingVars)) {
                log.info("台账流程实例中不存在变更记录字段: {}，将创建新的记录列表", changeRecordField);
                return new java.util.ArrayList<>();
            }

            String existingJson = (String) existingVars.get(0).get("TEXT_");
            log.info("台账中现有的变更记录JSON: {}", existingJson);

            if (StrUtil.isBlank(existingJson) || "null".equals(existingJson)) {
                log.info("现有变更记录为空，创建新的记录列表");
                return new java.util.ArrayList<>();
            }

            // 解析现有的变更记录
            try {
                Object existingRecords = JsonUtils.parseObject(existingJson, Object.class);
                if (existingRecords instanceof List) {
                    List<Map<String, Object>> recordList = (List<Map<String, Object>>) existingRecords;
                    log.info("成功解析现有变更记录，数量: {}", recordList.size());
                    return recordList;
                } else {
                    log.warn("现有变更记录不是List格式，重新初始化。原数据: {}", existingJson);
                    return new java.util.ArrayList<>();
                }
            } catch (Exception parseEx) {
                log.error("解析现有变更记录JSON失败，重新初始化。原数据: {}", existingJson, parseEx);
                return new java.util.ArrayList<>();
            }

        } catch (Exception e) {
            log.error("获取现有变更记录失败，流程ID: {}, 字段: {}", ledgerProcessInstanceId, changeRecordField, e);
            return new java.util.ArrayList<>();
        }
    }

    /**
     * 解析新的变更记录
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> parseChangeRecords(Object changeRecordValue) {
        try {
            log.info("开始解析新的变更记录，数据类型: {}", changeRecordValue.getClass().getSimpleName());

            if (changeRecordValue instanceof List) {
                List<Map<String, Object>> recordList = (List<Map<String, Object>>) changeRecordValue;
                log.info("变更记录已经是List格式，数量: {}", recordList.size());
                return recordList;
            } else if (changeRecordValue instanceof String) {
                String jsonStr = (String) changeRecordValue;
                log.info("解析变更记录JSON字符串: {}", jsonStr);

                if (StrUtil.isBlank(jsonStr) || "null".equals(jsonStr)) {
                    log.info("变更记录JSON为空");
                    return new java.util.ArrayList<>();
                }

                Object parsed = JsonUtils.parseObject(jsonStr, Object.class);
                if (parsed instanceof List) {
                    List<Map<String, Object>> recordList = (List<Map<String, Object>>) parsed;
                    log.info("成功解析变更记录JSON，数量: {}", recordList.size());
                    return recordList;
                } else {
                    log.warn("变更记录JSON不是List格式: {}", jsonStr);
                    return new java.util.ArrayList<>();
                }
            } else if (changeRecordValue instanceof Map) {
                // 如果是单个Map对象，包装成List
                Map<String, Object> singleRecord = (Map<String, Object>) changeRecordValue;
                log.info("变更记录是单个Map对象，包装成List: {}", singleRecord);
                List<Map<String, Object>> recordList = new java.util.ArrayList<>();
                recordList.add(singleRecord);
                return recordList;
            } else {
                log.warn("不支持的变更记录格式: {}, 数据: {}", changeRecordValue.getClass(), changeRecordValue);
                return new java.util.ArrayList<>();
            }
        } catch (Exception e) {
            log.error("解析变更记录失败，数据: {}", changeRecordValue, e);
            return new java.util.ArrayList<>();
        }
    }

    /**
     * 根据字段映射构建新的变更记录
     */
    private Map<String, Object> buildChangeRecord(Map<String, Object> formVariables,
                                                 HistoricProcessInstance changeProcessInstance,
                                                 ListenerConfig config) {
        Map<String, Object> changeRecord = new java.util.HashMap<>();

        try {
            log.info("开始构建变更记录，字段映射: {}", config.getFieldMapping());
            log.info("表单变量中可用的字段: {}", formVariables.keySet());

            // 根据字段映射添加变更字段
            if (config.getFieldMapping() != null && !config.getFieldMapping().isEmpty()) {
                for (Map.Entry<String, String> mapping : config.getFieldMapping().entrySet()) {
                    String sourceField = mapping.getKey();    // 变更流程中的字段名
                    String targetField = mapping.getValue();  // 变更记录中的字段名

                    Object value = getNestedFieldValue(formVariables, sourceField);
                    if (value != null) {
                        // 清理值，避免双重JSON序列化
                        Object cleanValue = cleanValue(value);
                        changeRecord.put(targetField, cleanValue);
                        log.info("映射字段: {} -> {}, 值: {}", sourceField, targetField, cleanValue);
                    } else {
                        log.info("源字段 {} 的值为空，跳过映射", sourceField);
                    }
                }
            } else {
                log.warn("未配置字段映射，只添加基础信息");
                // 如果没有配置映射，添加默认的基础信息
                changeRecord.put("applyDate", LocalDateTime.now().toLocalDate().toString());
                changeRecord.put("applyUser", changeProcessInstance.getStartUserId());
                changeRecord.put("changeTime", LocalDateTime.now().toString());
            }

            log.info("构建完成的变更记录: {}", changeRecord);
            return changeRecord;

        } catch (Exception e) {
            log.error("构建变更记录失败", e);
            return new java.util.HashMap<>();
        }
    }

    /**
     * 获取嵌套字段的值，支持点号分隔的路径和数组索引
     * 例如：
     * - "customerInvoicingInformationAttachment" -> 直接获取根级字段
     * - "invoiceInformation[0].customerInvoicingInformationAttachment" -> 获取数组第一个元素的字段
     * - "invoiceInformation.customerInvoicingInformationAttachment" -> 获取数组第一个元素的字段（简化写法）
     */
    @SuppressWarnings("unchecked")
    private Object getNestedFieldValue(Map<String, Object> formVariables, String fieldPath) {
        try {
            log.debug("获取嵌套字段值: {}", fieldPath);

            // 如果不包含点号，直接获取根级字段
            if (!fieldPath.contains(".") && !fieldPath.contains("[")) {
                Object value = formVariables.get(fieldPath);
                log.debug("直接获取字段 {} 的值: {}", fieldPath, value);
                return value;
            }

            // 处理嵌套字段路径
            String[] pathParts = fieldPath.split("\\.");
            Object current = formVariables;

            for (int i = 0; i < pathParts.length; i++) {
                String part = pathParts[i];
                log.debug("处理路径部分: {}, 当前对象类型: {}", part, current != null ? current.getClass().getSimpleName() : "null");

                if (current == null) {
                    log.debug("当前对象为null，返回null");
                    return null;
                }

                // 处理数组索引，如 "invoiceInformation[0]"
                if (part.contains("[") && part.contains("]")) {
                    String fieldName = part.substring(0, part.indexOf("["));
                    String indexStr = part.substring(part.indexOf("[") + 1, part.indexOf("]"));

                    if (current instanceof Map) {
                        Object arrayField = ((Map<String, Object>) current).get(fieldName);
                        if (arrayField instanceof java.util.List) {
                            java.util.List<?> list = (java.util.List<?>) arrayField;
                            try {
                                int index = Integer.parseInt(indexStr);
                                if (index >= 0 && index < list.size()) {
                                    current = list.get(index);
                                    log.debug("获取数组 {} 索引 {} 的值: {}", fieldName, index, current);
                                } else {
                                    log.debug("数组索引 {} 超出范围，数组大小: {}", index, list.size());
                                    return null;
                                }
                            } catch (NumberFormatException e) {
                                log.warn("无效的数组索引: {}", indexStr);
                                return null;
                            }
                        } else {
                            log.debug("字段 {} 不是数组类型: {}", fieldName, arrayField != null ? arrayField.getClass().getSimpleName() : "null");
                            return null;
                        }
                    } else {
                        log.debug("当前对象不是Map类型，无法获取字段: {}", fieldName);
                        return null;
                    }
                } else {
                    // 处理普通字段
                    if (current instanceof Map) {
                        Object fieldValue = ((Map<String, Object>) current).get(part);

                        // 如果是最后一个路径部分，直接返回值
                        if (i == pathParts.length - 1) {
                            log.debug("获取最终字段 {} 的值: {}", part, fieldValue);
                            return fieldValue;
                        }

                        // 如果不是最后一个部分，但字段值是数组，默认取第一个元素
                        if (fieldValue instanceof java.util.List) {
                            java.util.List<?> list = (java.util.List<?>) fieldValue;
                            if (!list.isEmpty()) {
                                current = list.get(0);
                                log.debug("字段 {} 是数组，取第一个元素: {}", part, current);
                            } else {
                                log.debug("字段 {} 是空数组", part);
                                return null;
                            }
                        } else {
                            current = fieldValue;
                            log.debug("获取字段 {} 的值: {}", part, current);
                        }
                    } else {
                        log.debug("当前对象不是Map类型，无法获取字段: {}", part);
                        return null;
                    }
                }
            }

            log.debug("最终获取的值: {}", current);
            return current;

        } catch (Exception e) {
            log.error("获取嵌套字段值失败: {}", fieldPath, e);
            return null;
        }
    }

    /**
     * 清理值，避免双重JSON序列化
     */
    private Object cleanValue(Object value) {
        if (value instanceof String) {
            String strValue = (String) value;
            // 如果是被双重序列化的字符串，尝试清理
            if (strValue.startsWith("\"") && strValue.endsWith("\"") && strValue.length() > 2) {
                return strValue.substring(1, strValue.length() - 1);
            }
            return strValue;
        } else if (value instanceof java.util.List) {
            // 如果是List，直接返回（保持数组格式）
            return value;
        } else if (value instanceof Number || value instanceof Boolean) {
            // 数字和布尔值直接返回
            return value;
        }
        return value;
    }

    /**
     * 更新历史变量（专门用于JSON数组）
     */
    private void updateHistoricVariableAsJson(String processInstanceId, String variableName, List<Map<String, Object>> value) {
        try {
            // 将List直接序列化为JSON数组
            String jsonValue = JsonUtils.toJsonString(value);
            log.debug("更新JSON数组变量: {}, 值: {}", variableName, jsonValue);

            // 先删除现有的历史变量
            String deleteSql = "DELETE FROM ACT_HI_VARINST WHERE PROC_INST_ID_ = ? AND NAME_ = ?";
            int deleted = jdbcTemplate.update(deleteSql, processInstanceId, variableName);
            log.debug("删除现有历史变量: processInstanceId={}, variableName={}, deleted={}",
                    processInstanceId, variableName, deleted);

            // 插入新的历史变量
            String insertSql = "INSERT INTO ACT_HI_VARINST (ID_, PROC_INST_ID_, EXECUTION_ID_, TASK_ID_, NAME_, VAR_TYPE_, REV_, BYTEARRAY_ID_, DOUBLE_, LONG_, TEXT_, TEXT2_, CREATE_TIME_, LAST_UPDATED_TIME_) " +
                              "VALUES (?, ?, ?, NULL, ?, 'json', 1, NULL, NULL, NULL, ?, NULL, NOW(), NOW())";

            // 生成标准的UUID格式ID
            String varId = java.util.UUID.randomUUID().toString();

            int inserted = jdbcTemplate.update(insertSql, varId, processInstanceId, processInstanceId, variableName, jsonValue);
            log.debug("插入JSON数组历史变量: processInstanceId={}, variableName={}, varId={}, inserted={}",
                    processInstanceId, variableName, varId, inserted);

        } catch (Exception e) {
            log.error("更新JSON数组历史变量失败: processInstanceId={}, variableName={}, value={}",
                    processInstanceId, variableName, value, e);
            // 不抛出异常，避免影响其他字段的更新
            log.warn("跳过JSON数组字段 {} 的更新，继续处理其他字段", variableName);
        }
    }

    /**
     * 检查是否应该跳过该字段
     */
    private boolean shouldSkipField(String fieldName, ListenerConfig config) {
        // 1. 检查是否在配置的跳过字段列表中
        if (config.getSkipFields() != null && config.getSkipFields().contains(fieldName)) {
            return true;
        }

        // 2. 检查是否是关联字段
        if (config.getRelationField() != null && config.getRelationField().equals(fieldName)) {
            return true;
        }

        // 3. 检查是否是变更记录字段
        if (config.getChangeRecordField() != null && config.getChangeRecordField().equals(fieldName)) {
            return true;
        }

        return false;
    }

    /**
     * 检查台账中是否已存在该变量
     */
    private boolean isVariableExistsInLedger(String ledgerProcessInstanceId, String variableName) {
        try {
            String sql = "SELECT COUNT(*) FROM ACT_HI_VARINST WHERE PROC_INST_ID_ = ? AND NAME_ = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, ledgerProcessInstanceId, variableName);
            boolean exists = count != null && count > 0;
            log.debug("检查台账变量存在性: processInstanceId={}, variableName={}, exists={}",
                    ledgerProcessInstanceId, variableName, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查台账变量存在性失败: processInstanceId={}, variableName={}",
                    ledgerProcessInstanceId, variableName, e);
            // 出错时返回false，跳过更新
            return false;
        }
    }
}
